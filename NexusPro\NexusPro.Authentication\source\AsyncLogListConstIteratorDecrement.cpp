﻿/*
 * AsyncLogListConstIteratorDecrement.cpp
 * Original Function: ??F?$_Const_iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@AEAV012@XZ
 * Address: 0x1403C5BF0
 *
 * STL list const iterator decrement operator for async log containers.
 * Moves the iterator to the previous element in the list.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <memory>
#include <utility>

/**
 * Decrement operator for list const iterator
 * Moves the iterator to the previous element in the list
 * @param this_ptr Pointer to the const iterator object
 * @param a2 Node pointer parameter
 * @return Reference to the decremented iterator
 */
std::list<std::pair<int const, CAsyncLogInfo*>>::_Const_iterator<0>&
AsyncLogListConstIteratorDecrement(
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Const_iterator<0>* this_ptr,
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Node* a2)
{
    __int64* v2; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v5; // [sp+0h] [bp-28h]@1
    void** v6; // [sp+30h] [bp+8h]@1

    v6 = (void**)this_ptr;
    v2 = &v5;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v2 = 0xCCCCCCCC;
        v2 = (__int64*)((char*)v2 + 4);
    }

    // Move to previous node
    v6->_Ptr = std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>::_Prevnode(
        (std::list<std::pair<int const, CAsyncLogInfo*>, std::allocator<std::pair<int const, CAsyncLogInfo*>>>*)v6->_Ptr,
        a2);

    return *this_ptr;
}





