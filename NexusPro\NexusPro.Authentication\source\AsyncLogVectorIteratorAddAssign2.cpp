/**
 * @file AsyncLogVectorIteratorAddAssign2.cpp
 * @brief RF Online Async Log Vector Iterator Add-Assign Operator (Non-Const Version)
 * @note Original Function: ??Y?$_Vector_iterator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@V?$allocator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@3@@std@@AEAV01@_J@Z
 * @note Original Address: 0x1403C6DD0
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: Y_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C6DD0.cpp
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <iterator>
#include <list>
#include <memory>
#include <utility>
#include <vector>

/**
 * @brief Add-assign operator for async log vector iterator (non-const version)
 * @param this Pointer to the vector iterator instance
 * @param _Off Offset to add to the iterator
 * @return Reference to the modified iterator
 *
 * This function implements the add-assign operator (it += n) for the async log vector iterator.
 * It advances the iterator by the specified offset and returns a reference to itself.
 * This is the non-const version that delegates to the const iterator implementation.
 */
AsyncLogVectorIterator* AsyncLogVectorIterator_AddAssign2(
    AsyncLogVectorIterator* this,
    __int64 _Off) {
    
    // Local variables with meaningful names (original decompiled names in comments)
    __int64* bufferPointer;           // Original: v2 (rdi register)
    signed __int64 loopCounter;       // Original: i (rcx register)
    __int64 stackBuffer[8];           // Original: v5 (stack buffer [sp+0h] [bp-28h])
    AsyncLogVectorIterator* currentIterator; // Original: v6 ([sp+30h] [bp+8h])

    // Initialize current iterator reference
    currentIterator = this;
    
    // Initialize memory buffer with debug pattern (0xCCCCCCCC)
    bufferPointer = stackBuffer;
    for (loopCounter = 8; loopCounter > 0; --loopCounter) {
        // Fill buffer with debug pattern
        *reinterpret_cast<DWORD*>(bufferPointer) = 0xCCCCCCCC;
        
        // Move to next DWORD position
        bufferPointer = reinterpret_cast<__int64*>(
            reinterpret_cast<char*>(bufferPointer) + 4
        );
    }
    
    // Call the base class add-assign operator
    // This delegates to the const iterator version which handles the actual pointer arithmetic
    AsyncLogVectorConstIterator_AddAssign(
        reinterpret_cast<AsyncLogVectorConstIterator*>(&currentIterator->_Mycont),
        _Off
    );
    
    return currentIterator;
}
