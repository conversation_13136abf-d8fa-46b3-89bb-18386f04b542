/**
 * @file AsyncLogVectorIteratorAddition.cpp
 * @brief RF Online Async Log Vector Iterator Addition Operator
 * @note Original Function: ??H?$_Vector_iterator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@V?$allocator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@3@@std@@QEBA?AV01@_J@Z
 * @note Original Address: 0x1403C5C70
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: H_Vector_iteratorV_Iterator0AlistUpairCBHPEAVCAsyn_1403C5C70.cpp
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <iterator>
#include <list>
#include <utility>
#include <vector>

/**
 * @brief Addition operator for async log vector iterator
 * @param this Pointer to the vector iterator instance
 * @param result Pointer to store the result iterator
 * @param _Off Offset to add to the iterator
 * @return New iterator pointing to the position offset by _Off
 *
 * This function implements the addition operator (it + n) for the async log vector iterator.
 * It creates a new iterator that points to a position offset by the specified amount.
 */
AsyncLogVectorIterator* AsyncLogVectorIterator_Addition(
    AsyncLogVectorIterator* this,
    AsyncLogVectorIterator* result,
    __int64 _Off) {
    
    // Local variables with meaningful names (original decompiled names in comments)
    __int64* bufferPointer;           // Original: v3 (rdi register)
    signed __int64 loopCounter;       // Original: i (rcx register)
    AsyncLogVectorIterator* tempResult; // Original: v5 (rax register)
    __int64 stackBuffer[20];          // Original: v7 (stack buffer [sp+0h] [bp-58h])
    AsyncLogVectorIterator tempIterator; // Original: v8 ([sp+28h] [bp-30h])
    int flags;                        // Original: v9 ([sp+44h] [bp-14h])
    __int64 framePointer;             // Original: v10 ([sp+48h] [bp-10h])
    AsyncLogVectorIterator* currentIterator; // Original: __that ([sp+60h] [bp+8h])
    AsyncLogVectorIterator* resultIterator;  // Original: v12 ([sp+68h] [bp+10h])
    __int64 offset;                   // Original: _Offa ([sp+70h] [bp+18h])

    // Initialize parameters
    offset = _Off;
    resultIterator = result;
    currentIterator = this;
    
    // Initialize memory buffer with debug pattern (0xCCCCCCCC)
    bufferPointer = stackBuffer;
    for (loopCounter = 20; loopCounter > 0; --loopCounter) {
        // Fill buffer with debug pattern
        *reinterpret_cast<DWORD*>(bufferPointer) = 0xCCCCCCCC;
        
        // Move to next DWORD position
        bufferPointer = reinterpret_cast<__int64*>(
            reinterpret_cast<char*>(bufferPointer) + 4
        );
    }
    
    // Initialize frame pointer and flags
    framePointer = -2LL;
    flags = 0;
    
    // Create a copy of the current iterator
    AsyncLogVectorIterator_CopyConstructor(&tempIterator, currentIterator);
    
    // Add the offset to the temporary iterator
    tempResult = AsyncLogVectorIterator_AddAssign(&tempIterator, offset);
    
    // Copy the result to the output iterator
    AsyncLogVectorIterator_CopyConstructor(resultIterator, tempResult);
    
    // Set completion flag
    flags |= 1u;
    
    // Clean up temporary iterator
    AsyncLogVectorIterator_Destructor(&tempIterator);
    
    return resultIterator;
}
