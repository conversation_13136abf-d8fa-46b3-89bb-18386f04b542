#!/usr/bin/env python3
"""
Quick execution script for the Authentication module file renaming automation.
Run this script from the Authentication module directory.
"""

import sys
import os
from pathlib import Path

# Add current directory to path to import the renamer
sys.path.insert(0, str(Path(__file__).parent))

try:
    from rename_authentication_files import AuthenticationFileRenamer
    
    def main():
        print("🚀 Starting Authentication Module File Renaming Automation")
        print("=" * 70)
        
        # Confirm with user
        response = input("\n⚠️  This will rename ALL files with long decompiled names in the Authentication module.\nDo you want to continue? (y/N): ")
        
        if response.lower() not in ['y', 'yes']:
            print("❌ Operation cancelled by user.")
            return
        
        # Create renamer instance
        base_path = "."  # Current directory
        renamer = AuthenticationFileRenamer(base_path)
        
        # Run the renaming process
        print("\n🔄 Processing files...")
        results = renamer.run()
        
        # Print final summary
        print("\n" + "=" * 70)
        print("✅ RENAMING COMPLETE")
        print("=" * 70)
        print(f"📁 Source files renamed: {results['source_files']}")
        print(f"📄 Header files renamed: {results['header_files']}")
        print(f"📊 Total files renamed: {results['total_files']}")
        print(f"❌ Errors encountered: {results['errors']}")
        
        if results['errors'] > 0:
            print(f"\n⚠️  {results['errors']} errors occurred. Check file_rename_report.md for details.")
        else:
            print("\n🎉 All files renamed successfully!")
            
        print(f"\n📋 Detailed report saved to: file_rename_report.md")
        print("\n💡 Next steps:")
        print("   1. Review the renamed files")
        print("   2. Update Visual Studio project files if needed")
        print("   3. Test compilation")
        print("   4. Update README.md with the changes")

    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"❌ Error importing renamer module: {e}")
    print("Make sure you're running this script from the Authentication module directory.")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    sys.exit(1)
