﻿/*
 * AsyncLogListIteratorIncrement.cpp
 * Original Function: ??E?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@AEAV012@XZ
 * Address: 0x1403C4350
 *
 * STL list iterator increment operator for async log containers.
 * Advances the iterator to the next element in the list.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <utility>

/**
 * Increment operator for list iterator
 * Advances the iterator to the next element in the list
 * @param this_ptr Pointer to the iterator object
 * @return Reference to the incremented iterator
 */
std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>&
AsyncLogListIteratorIncrement(std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>* this_ptr)
{
    __int64* v1; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-28h]@1
    void** v5; // [sp+30h] [bp+8h]@1

    v5 = (void**)this_ptr;
    v1 = &v4;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v1 = 0xCCCCCCCC;
        v1 = (__int64*)((char*)v1 + 4);
    }

    // Increment using const iterator base
    std::list<std::pair<int const, CAsyncLogInfo*>>::_Const_iterator<0>::operator++(
        (std::list<std::pair<int const, CAsyncLogInfo*>>::_Const_iterator<0>*)&v5->_Mycont);

    return *this_ptr;
}





