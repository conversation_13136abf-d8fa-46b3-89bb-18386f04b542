#!/usr/bin/env python3
"""
RF Online Authentication Module File Renaming Automation
========================================================

This script systematically renames all files with long decompiled names in the 
Authentication module to shorter, meaningful names while preserving the original 
functionality and maintaining a mapping log.

Features:
- Renames both .cpp and .h files
- Creates meaningful short names based on function patterns
- Maintains original file content with updated headers
- Logs all changes for tracking
- Handles both source and header directories
- Preserves Visual Studio 2022 compatibility

Author: NexusPro Development Team
Date: 2025-01-14
"""

import os
import re
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Optional

class AuthenticationFileRenamer:
    def __init__(self, base_path: str):
        self.base_path = Path(base_path)
        self.source_dir = self.base_path / "source"
        self.headers_dir = self.base_path / "headers"
        self.rename_log = []
        self.error_log = []
        
        # Enhanced pattern mappings for generating meaningful names
        self.name_patterns = {
            # STL Container Operations - AsyncLog
            r'.*list.*pair.*CAsyncLogInfo.*': 'AsyncLogList',
            r'.*vector.*Iterator.*CAsyncLogInfo.*': 'AsyncLogVector',
            r'.*hash.*map.*CAsyncLogInfo.*': 'AsyncLogHashMap',
            r'.*allocator.*CAsyncLogInfo.*': 'AsyncLogAllocator',
            r'.*iterator.*CAsyncLogInfo.*': 'AsyncLogIterator',
            r'.*CAsyncLogInfo.*': 'AsyncLog',

            # Authentication Operations
            r'.*AccountServerLogin.*': 'AccountServerLogin',
            r'.*BillingLogin.*': 'BillingLogin',
            r'.*Login.*': 'Login',
            r'.*Auth.*Ticket.*': 'AuthTicket',
            r'.*Auth.*Key.*': 'AuthKey',
            r'.*Auth.*Mining.*': 'AuthMining',
            r'.*Billing.*': 'Billing',
            r'.*Session.*': 'Session',
            r'.*Verify.*': 'Verify',
            r'.*Connect.*': 'Connect',
            r'.*Disconnect.*': 'Disconnect',

            # Crypto Operations
            r'.*MessageAuthenticationCode.*HMAC.*': 'HMACMessageAuth',
            r'.*DL_PrivateKey.*': 'DLPrivateKey',
            r'.*DL_PublicKey.*': 'DLPublicKey',
            r'.*DL_GroupParameters.*': 'DLGroupParams',
            r'.*ValidateParameters.*': 'ValidateParams',
            r'.*ValidateGroup.*': 'ValidateGroup',
            r'.*ValidateElement.*': 'ValidateElement',
            r'.*Crypto.*': 'Crypto',
            r'.*HMAC.*': 'HMAC',
            r'.*Hash.*': 'Hash',
            r'.*Validate.*': 'Validate',
            r'.*Key.*': 'Key',

            # STL Operations
            r'.*_Construct.*': 'Construct',
            r'.*_Destroy.*': 'Destroy',
            r'.*_Allocate.*': 'Allocate',
            r'.*_Deallocate.*': 'Deallocate',
            r'.*_Insert.*': 'Insert',
            r'.*_Erase.*': 'Erase',
            r'.*_Copy.*': 'Copy',
            r'.*_Move.*': 'Move',
            r'.*_Fill.*': 'Fill',
            r'.*_Uninit.*': 'Uninit',
            r'.*_Buynode.*': 'BuyNode',
            r'.*_Tidy.*': 'Tidy',
            r'.*_Splice.*': 'Splice',

            # Iterator Operations
            r'.*begin.*': 'Begin',
            r'.*end.*': 'End',
            r'.*size.*': 'Size',
            r'.*capacity.*': 'Capacity',
            r'.*clear.*': 'Clear',
            r'.*resize.*': 'Resize',
            r'.*insert.*': 'Insert',
            r'.*erase.*': 'Erase',
            r'.*find.*': 'Find',

            # Game Specific Operations
            r'.*GuildBattle.*': 'GuildBattle',
            r'.*AutoTrade.*': 'AutoTrade',
            r'.*HackShield.*': 'HackShield',
            r'.*Nation.*': 'Nation',
            r'.*InvalidateDevice.*': 'InvalidateDevice',
            r'.*Invalidate.*': 'Invalidate',

            # Jump Functions
            r'^j_.*': 'Jump',

            # Destructors
            r'.*dtor.*': 'Destructor',
            r'.*_1_dtor.*': 'Destructor',

            # STL Template Specializations
            r'.*stdext_Hash.*': 'StdExtHash',
            r'.*stdlist.*': 'StdList',
            r'.*stdvector.*': 'StdVector',
            r'.*std_.*': 'Std',
        }
    
    def generate_meaningful_name(self, original_name: str) -> str:
        """Generate a meaningful short name from the original decompiled name."""
        # Remove file extension for processing
        name_without_ext = original_name.replace('.cpp', '').replace('.h', '')
        
        # Extract address if present
        address_match = re.search(r'_([0-9A-F]{8,})$', name_without_ext)
        address = address_match.group(1) if address_match else None
        
        # Try to match patterns and generate meaningful names
        for pattern, base_name in self.name_patterns.items():
            if re.search(pattern, name_without_ext, re.IGNORECASE):
                # Add specific suffixes based on additional patterns
                suffix = self._get_operation_suffix(name_without_ext)
                
                # Create unique name with counter if needed
                candidate_name = f"{base_name}{suffix}"
                return self._ensure_unique_name(candidate_name, original_name)
        
        # Fallback: create name from first meaningful part
        return self._create_fallback_name(name_without_ext, address)
    
    def _get_operation_suffix(self, name: str) -> str:
        """Get operation-specific suffix for the name."""
        name_lower = name.lower()

        # STL Iterator operations (most specific first)
        if 'const_iterator' in name_lower:
            if 'inequality' in name_lower or '9_vector_const' in name_lower:
                return 'ConstIteratorInequality'
            elif 'equality' in name_lower or '8_vector_const' in name_lower:
                return 'ConstIteratorEquality'
            elif 'addassign' in name_lower or 'y_vector_const' in name_lower:
                return 'ConstIteratorAddAssign'
            elif 'copyconstructor' in name_lower or '0_vector_const' in name_lower:
                return 'ConstIteratorCopyConstructor'
            elif 'destructor' in name_lower or '1_vector_const' in name_lower:
                return 'ConstIteratorDestructor'
            elif 'ptrconstructor' in name_lower:
                return 'ConstIteratorPtrConstructor'
            else:
                return 'ConstIterator'

        elif 'iterator' in name_lower:
            if 'postincrement' in name_lower or 'e_iterator' in name_lower:
                return 'IteratorPostIncrement'
            elif 'addition' in name_lower or 'h_vector_iterator' in name_lower:
                return 'IteratorAddition'
            elif 'addassign' in name_lower or 'y_vector_iterator' in name_lower:
                return 'IteratorAddAssign'
            elif 'copyconstructor' in name_lower or '0_iterator' in name_lower:
                return 'IteratorCopyConstructor'
            elif 'destructor' in name_lower or '1_iterator' in name_lower:
                return 'IteratorDestructor'
            elif 'maxsize' in name_lower:
                return 'IteratorMaxSize'
            elif 'ptrconstructor' in name_lower:
                return 'IteratorPtrConstructor'
            elif 'allocate' in name_lower:
                return 'IteratorAllocate'
            elif 'deallocate' in name_lower:
                return 'IteratorDeallocate'
            else:
                return 'Iterator'

        # Container operations
        elif 'buynode' in name_lower:
            return 'BuyNode'
        elif 'construct' in name_lower and 'allocator' in name_lower:
            return 'AllocatorConstruct'
        elif 'construct' in name_lower:
            return 'Constructor'
        elif 'destroy' in name_lower and 'allocator' in name_lower:
            return 'AllocatorDestroy'
        elif 'destroy' in name_lower:
            return 'Destructor'
        elif 'allocate' in name_lower and 'allocator' in name_lower:
            return 'AllocatorAllocate'
        elif 'allocate' in name_lower:
            return 'Allocate'
        elif 'deallocate' in name_lower:
            return 'Deallocate'

        # List operations
        elif 'begin' in name_lower:
            return 'Begin'
        elif 'end' in name_lower:
            return 'End'
        elif 'size' in name_lower:
            return 'Size'
        elif 'capacity' in name_lower:
            return 'Capacity'
        elif 'clear' in name_lower:
            return 'Clear'
        elif 'resize' in name_lower:
            return 'Resize'
        elif 'insert' in name_lower:
            return 'Insert'
        elif 'erase' in name_lower:
            return 'Erase'
        elif 'find' in name_lower:
            return 'Find'
        elif 'lower_bound' in name_lower:
            return 'LowerBound'

        # Authentication operations
        elif 'accountserverlogin' in name_lower:
            return 'AccountServerLogin'
        elif 'billinglogin' in name_lower:
            return 'BillingLogin'
        elif 'login' in name_lower:
            return 'Login'
        elif 'logout' in name_lower:
            return 'Logout'
        elif 'verify' in name_lower:
            return 'Verify'
        elif 'connect' in name_lower:
            return 'Connect'
        elif 'disconnect' in name_lower:
            return 'Disconnect'

        # Crypto operations
        elif 'validateparameters' in name_lower:
            return 'ValidateParameters'
        elif 'validategroup' in name_lower:
            return 'ValidateGroup'
        elif 'validateelement' in name_lower:
            return 'ValidateElement'
        elif 'validate' in name_lower:
            return 'Validate'
        elif 'generatekeypair' in name_lower:
            return 'GenerateKeyPair'
        elif 'generate' in name_lower:
            return 'Generate'

        # Game specific
        elif 'guildbattle' in name_lower:
            return 'GuildBattle'
        elif 'autotrade' in name_lower:
            return 'AutoTrade'
        elif 'hackshield' in name_lower:
            return 'HackShield'
        elif 'invalidatedevice' in name_lower:
            return 'InvalidateDevice'
        elif 'invalidate' in name_lower:
            return 'Invalidate'

        # Jump functions
        elif name_lower.startswith('j_'):
            return 'Jump'

        # Destructors
        elif 'dtor' in name_lower:
            return 'Destructor'

        return ''
    
    def _ensure_unique_name(self, candidate_name: str, original_name: str) -> str:
        """Ensure the generated name is unique by adding a counter if needed."""
        base_name = candidate_name
        counter = 1
        
        # Check if name already exists in our rename log
        existing_names = [entry['new_name'].replace('.cpp', '').replace('.h', '') 
                         for entry in self.rename_log]
        
        while candidate_name in existing_names:
            candidate_name = f"{base_name}{counter}"
            counter += 1
        
        return candidate_name
    
    def _create_fallback_name(self, name: str, address: Optional[str]) -> str:
        """Create a fallback name when no patterns match."""
        # Take first meaningful part (up to 20 chars)
        clean_name = re.sub(r'[^a-zA-Z0-9]', '', name[:20])
        
        if address:
            return f"Func_{clean_name}_{address[-6:]}"
        else:
            return f"Func_{clean_name}"
    
    def rename_file(self, file_path: Path, new_name: str) -> bool:
        """Rename a single file and update its content."""
        try:
            # Determine new file path
            new_file_path = file_path.parent / f"{new_name}{file_path.suffix}"
            
            # Read original content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Update file header with new name and original reference
            updated_content = self._update_file_header(content, new_name, file_path.name)
            
            # Write to new file
            with open(new_file_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            # Remove original file
            file_path.unlink()
            
            # Log the rename
            self.rename_log.append({
                'original_name': file_path.name,
                'new_name': new_file_path.name,
                'directory': str(file_path.parent.relative_to(self.base_path)),
                'status': 'SUCCESS'
            })
            
            return True
            
        except Exception as e:
            self.error_log.append({
                'file': str(file_path),
                'error': str(e),
                'operation': 'rename'
            })
            return False
    
    def _update_file_header(self, content: str, new_name: str, original_name: str) -> str:
        """Update the file header with new name and original reference."""
        # Create new header comment
        new_header = f"""/**
 * @file {new_name}{'.cpp' if '.cpp' in original_name else '.h'}
 * @brief RF Online Authentication Module - {new_name.replace('_', ' ').title()}
 * @note Original File: {original_name}
 * @note Renamed for improved readability and maintainability
 * @note Decompiled from IDA Pro - Cleaned and refactored
 */

"""
        
        # Remove old header if present and add new one
        # Look for existing file header patterns
        header_patterns = [
            r'/\*\*.*?\*/',  # /** ... */
            r'/\*.*?\*/',    # /* ... */
        ]
        
        for pattern in header_patterns:
            content = re.sub(pattern, '', content, count=1, flags=re.DOTALL)
        
        # Add new header at the beginning
        return new_header + content.lstrip()
    
    def process_directory(self, directory: Path) -> int:
        """Process all files in a directory."""
        if not directory.exists():
            print(f"Directory {directory} does not exist")
            return 0
        
        files_processed = 0
        
        # Get all .cpp and .h files with long names
        for file_path in directory.glob("*"):
            if file_path.is_file() and file_path.suffix in ['.cpp', '.h']:
                # Skip files that already have short names
                if len(file_path.stem) > 30 or any(char in file_path.stem for char in ['_', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9']):
                    new_name = self.generate_meaningful_name(file_path.name)
                    
                    print(f"Renaming: {file_path.name} -> {new_name}{file_path.suffix}")
                    
                    if self.rename_file(file_path, new_name):
                        files_processed += 1
                    else:
                        print(f"Failed to rename: {file_path.name}")
        
        return files_processed
    
    def run(self) -> Dict[str, int]:
        """Run the complete renaming process."""
        print("Starting Authentication Module File Renaming...")
        print("=" * 60)
        
        # Process source files
        print(f"\nProcessing source files in: {self.source_dir}")
        source_count = self.process_directory(self.source_dir)
        
        # Process header files
        print(f"\nProcessing header files in: {self.headers_dir}")
        header_count = self.process_directory(self.headers_dir)
        
        # Generate summary report
        self._generate_report()
        
        return {
            'source_files': source_count,
            'header_files': header_count,
            'total_files': source_count + header_count,
            'errors': len(self.error_log)
        }
    
    def _generate_report(self):
        """Generate a detailed report of all changes."""
        report_path = self.base_path / "file_rename_report.md"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# Authentication Module File Renaming Report\n\n")
            f.write(f"Generated: {self._get_timestamp()}\n\n")
            
            # Summary
            f.write("## Summary\n\n")
            f.write(f"- Total files renamed: {len(self.rename_log)}\n")
            f.write(f"- Errors encountered: {len(self.error_log)}\n\n")
            
            # Successful renames
            f.write("## Successful Renames\n\n")
            f.write("| Original File | New Name | Directory | Status |\n")
            f.write("|---------------|----------|-----------|--------|\n")
            
            for entry in self.rename_log:
                f.write(f"| `{entry['original_name']}` | `{entry['new_name']}` | {entry['directory']} | ✅ {entry['status']} |\n")
            
            # Errors
            if self.error_log:
                f.write("\n## Errors\n\n")
                f.write("| File | Error | Operation |\n")
                f.write("|------|-------|----------|\n")
                
                for error in self.error_log:
                    f.write(f"| `{error['file']}` | {error['error']} | {error['operation']} |\n")
        
        print(f"\nDetailed report saved to: {report_path}")
    
    def _get_timestamp(self) -> str:
        """Get current timestamp."""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def main():
    """Main execution function."""
    # Set the base path to the Authentication module
    base_path = "."  # Current directory (should be run from Authentication module root)
    
    # Create renamer instance
    renamer = AuthenticationFileRenamer(base_path)
    
    # Run the renaming process
    results = renamer.run()
    
    # Print final summary
    print("\n" + "=" * 60)
    print("RENAMING COMPLETE")
    print("=" * 60)
    print(f"Source files renamed: {results['source_files']}")
    print(f"Header files renamed: {results['header_files']}")
    print(f"Total files renamed: {results['total_files']}")
    print(f"Errors encountered: {results['errors']}")
    
    if results['errors'] > 0:
        print(f"\n⚠️  {results['errors']} errors occurred. Check the report for details.")
    else:
        print("\n✅ All files renamed successfully!")

if __name__ == "__main__":
    main()
