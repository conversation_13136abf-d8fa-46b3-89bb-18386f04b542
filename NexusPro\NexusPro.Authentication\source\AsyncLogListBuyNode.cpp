/**
 * @file AsyncLogListBuyNode.cpp
 * @brief RF Online Async Log List Node Purchase/Allocation Function
 * @note Original Function: ?_Buynode@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@IEAAPEAU_Node@?$_List_nod@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@2@XZ
 * @note Original Address: 0x1403C6370
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: _BuynodelistUpairCBHPEAVCAsyncLogInfostdVallocator_1403C6370.cpp
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <list>
#include <memory>
#include <utility>

/**
 * @brief Allocates and initializes a new list node
 * @param this Pointer to the async log list instance
 * @return Pointer to the newly allocated and initialized list node
 *
 * This function allocates memory for a new list node and initializes its
 * next and previous pointers. It's part of the STL list implementation
 * for managing doubly-linked list nodes.
 */
AsyncLogListNode* AsyncLogList_BuyNode(AsyncLogList* this) {
    // Local variables with meaningful names (original decompiled names in comments)
    __int64* bufferPointer;           // Original: v1 (rdi register)
    signed __int64 loopCounter;       // Original: i (rcx register)
    AsyncLogListNode* nextNodePtr;    // Original: v3 (rdx register)
    AsyncLogListNode* prevNodePtr;    // Original: v4 (rdx register)
    __int64 stackBuffer[20];          // Original: v6 (stack buffer [sp+0h] [bp-58h])
    AsyncLogListNode* newNode;        // Original: v7 ([sp+28h] [bp-30h])
    int constructionFlags;            // Original: v8 ([sp+34h] [bp-24h])
    __int64 framePointer;             // Original: v9 ([sp+38h] [bp-20h])
    AsyncLogListNode** nextNodePtrPtr; // Original: _Ptr ([sp+40h] [bp-18h])
    AsyncLogListNode** prevNodePtrPtr; // Original: v11 ([sp+48h] [bp-10h])
    AsyncLogList* currentList;        // Original: v12 ([sp+60h] [bp+8h])

    // Initialize current list reference
    currentList = this;
    
    // Initialize memory buffer with debug pattern (0xCCCCCCCC)
    bufferPointer = stackBuffer;
    for (loopCounter = 20; loopCounter > 0; --loopCounter) {
        // Fill buffer with debug pattern
        *reinterpret_cast<DWORD*>(bufferPointer) = 0xCCCCCCCC;
        
        // Move to next DWORD position
        bufferPointer = reinterpret_cast<__int64*>(
            reinterpret_cast<char*>(bufferPointer) + 4
        );
    }
    
    // Initialize frame pointer
    framePointer = -2LL;
    
    // Allocate memory for a new list node using the node allocator
    newNode = reinterpret_cast<AsyncLogListNode*>(
        AsyncLogListNodeAllocator_Allocate(&currentList->_Alnod, 1ui64)
    );
    
    // Initialize construction flags
    constructionFlags = 0;
    
    // Get pointer to the next node pointer field and construct it
    nextNodePtrPtr = AsyncLogList_GetNextNodePtr(newNode, nextNodePtr);
    AsyncLogListNodePtrAllocator_Construct(
        &currentList->_Alptr,
        nextNodePtrPtr,
        reinterpret_cast<AsyncLogListNode* const*>(&newNode)
    );
    
    // Increment construction flags to track successful construction
    ++constructionFlags;
    
    // Get pointer to the previous node pointer field and construct it
    prevNodePtrPtr = AsyncLogList_GetPrevNodePtr(newNode, prevNodePtr);
    AsyncLogListNodePtrAllocator_Construct(
        &currentList->_Alptr,
        prevNodePtrPtr,
        reinterpret_cast<AsyncLogListNode* const*>(&newNode)
    );
    
    return newNode;
}
