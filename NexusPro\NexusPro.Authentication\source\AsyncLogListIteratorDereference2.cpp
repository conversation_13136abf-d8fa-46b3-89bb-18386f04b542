﻿/*
 * AsyncLogListIteratorDereference2.cpp
 * Original Function: ??D?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@QEBAAEAU?$std::pair@$$CBHPEAVCAsyncLogInfo@@@2@XZ
 * Address: 0x1403C4310
 *
 * STL list iterator dereference operator for async log containers.
 * Provides access to the element pointed to by the iterator.
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <utility>

/**
 * Dereference operator for list iterator (variant 2)
 * Provides access to the element pointed to by the iterator
 * @param this_ptr Pointer to the iterator object
 * @return Reference to the pair element
 */
std::pair<int const, CAsyncLogInfo*>&
AsyncLogListIteratorDereference2(const std::list<std::pair<int const, CAsyncLogInfo*>>::_Iterator<0>* this_ptr)
{
    __int64* v1; // rdi@1
    signed __int64 i; // rcx@1
    __int64 v4; // [sp+0h] [bp-28h]@1
    void** v5; // [sp+30h] [bp+8h]@1

    v5 = (void**)this_ptr;
    v1 = &v4;

    // Initialize debug pattern in local memory
    for (i = 8LL; i; --i) {
        *(DWORD*)v1 = 0xCCCCCCCC;
        v1 = (__int64*)((char*)v1 + 4);
    }

    // Return dereferenced iterator using const iterator base
    return std::list<std::pair<int const, CAsyncLogInfo*>>::_Const_iterator<0>::operator*(
        (std::list<std::pair<int const, CAsyncLogInfo*>>::_Const_iterator<0>*)&v5->_Mycont);
}





