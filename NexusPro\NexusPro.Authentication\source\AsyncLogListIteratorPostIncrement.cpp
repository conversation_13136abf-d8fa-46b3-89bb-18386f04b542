/**
 * @file AsyncLogListIteratorPostIncrement.cpp
 * @brief RF Online Async Log List Iterator Post-Increment Operator
 * @note Original Function: ??E?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@?AV012@H@Z
 * @note Original Address: 0x1403C7270
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: E_Iterator0AlistUpairCBHPEAVCAsyncLogInfostdValloc_1403C7270.cpp
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <iterator>
#include <list>
#include <utility>

/**
 * @brief Post-increment operator for async log list iterator
 * @param this Pointer to the iterator instance
 * @param result Pointer to store the result iterator
 * @param __formal Formal parameter for post-increment (unused)
 * @return Iterator pointing to the previous position
 *
 * This function implements the post-increment operator (it++) for the async log list iterator.
 * It creates a copy of the current iterator, advances the original, and returns the copy.
 */
AsyncLogListIterator* AsyncLogListIterator_PostIncrement(
    AsyncLogListIterator* this,
    AsyncLogListIterator* result,
    int __formal) {
    
    // Local variables with meaningful names (original decompiled names in comments)
    __int64* bufferPointer;           // Original: v3 (rdi register)
    signed __int64 loopCounter;       // Original: i (rcx register)
    __int64 stackBuffer[20];          // Original: v6 (stack buffer [sp+0h] [bp-58h])
    AsyncLogListIterator tempIterator; // Original: v7 ([sp+28h] [bp-30h])
    int flags;                        // Original: v8 ([sp+44h] [bp-14h])
    __int64 framePointer;             // Original: v9 ([sp+48h] [bp-10h])
    AsyncLogListIterator* currentIterator; // Original: __that ([sp+60h] [bp+8h])
    AsyncLogListIterator* resultIterator;  // Original: v11 ([sp+68h] [bp+10h])

    // Initialize result and current iterator pointers
    resultIterator = result;
    currentIterator = this;
    
    // Initialize memory buffer with debug pattern (0xCCCCCCCC)
    bufferPointer = stackBuffer;
    for (loopCounter = 20; loopCounter > 0; --loopCounter) {
        // Fill buffer with debug pattern
        *reinterpret_cast<DWORD*>(bufferPointer) = 0xCCCCCCCC;
        
        // Move to next DWORD position
        bufferPointer = reinterpret_cast<__int64*>(
            reinterpret_cast<char*>(bufferPointer) + 4
        );
    }
    
    // Initialize frame pointer and flags
    framePointer = -2LL;
    flags = 0;
    
    // Create a copy of the current iterator (for post-increment semantics)
    AsyncLogListIterator_CopyConstructor(&tempIterator, currentIterator);
    
    // Advance the original iterator (pre-increment)
    AsyncLogListIterator_PreIncrement(currentIterator);
    
    // Copy the saved iterator to the result
    AsyncLogListIterator_CopyConstructor(resultIterator, &tempIterator);
    
    // Set completion flag
    flags |= 1u;
    
    // Clean up temporary iterator
    AsyncLogListIterator_Destructor(&tempIterator);
    
    return resultIterator;
}
