/**
 * @file AsyncLogVectorConstIteratorInequality.cpp
 * @brief RF Online Async Log Vector Const Iterator Inequality Operator
 * @note Original Function: ??9?$_Vector_const_iterator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@V?$allocator@V?$_Iterator@$0A@@?$std::list@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@@3@@std@@QEBA_NAEBV01@@Z
 * @note Original Address: 0x1403C6E30
 * @note Decompiled from IDA Pro - Fixed syntax errors and improved readability
 * @note Original File: 9_Vector_const_iteratorV_Iterator0AlistUpairCBHPEA_1403C6E30.cpp
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <iterator>
#include <list>
#include <utility>
#include <vector>

/**
 * @brief Inequality operator for async log vector const iterator
 * @param this Pointer to the left-hand side iterator
 * @param _Right Pointer to the right-hand side iterator to compare against
 * @return true if iterators are not equal, false if they are equal
 *
 * This function implements the inequality operator (it1 != it2) for the async log vector const iterator.
 * It returns the negation of the equality comparison.
 */
bool AsyncLogVectorConstIterator_Inequality(
    AsyncLogVectorConstIterator* this,
    AsyncLogVectorConstIterator* _Right) {
    
    // Local variables with meaningful names (original decompiled names in comments)
    __int64* bufferPointer;           // Original: v2 (rdi register)
    signed __int64 loopCounter;       // Original: i (rcx register)
    __int64 stackBuffer[12];          // Original: v5 (stack buffer [sp+0h] [bp-38h])
    AsyncLogVectorConstIterator* leftIterator; // Original: v6 ([sp+40h] [bp+8h])

    // Initialize left iterator reference
    leftIterator = this;
    
    // Initialize memory buffer with debug pattern (0xCCCCCCCC)
    bufferPointer = stackBuffer;
    for (loopCounter = 12; loopCounter > 0; --loopCounter) {
        // Fill buffer with debug pattern
        *reinterpret_cast<DWORD*>(bufferPointer) = 0xCCCCCCCC;
        
        // Move to next DWORD position
        bufferPointer = reinterpret_cast<__int64*>(
            reinterpret_cast<char*>(bufferPointer) + 4
        );
    }
    
    // Return the negation of equality comparison
    // If iterators are equal, return false; if not equal, return true
    return !AsyncLogVectorConstIterator_Equality(leftIterator, _Right);
}
